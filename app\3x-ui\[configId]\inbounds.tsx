import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { InboundConfig } from '@/panels/3x-ui/types';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIInboundList } from '@/panels/3x-ui/utils';
import { smartFetch } from '@/lib/utils';
import InboundCard from '@/panels/3x-ui/components/InboundCard';
import ExportLinksModal from '@/panels/3x-ui/components/ExportLinksModal';
import { InboundSkeletonCard } from '~/panels/3x-ui/components/InboundSkeletonCard';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, ArrowUp, ArrowDown, Users } from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import React, { useCallback, useRef, useState, useMemo } from 'react';
import { SafeAreaView, StyleSheet, View, ScrollView, Alert } from 'react-native';

export default function InboundsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig } = useAppStore();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [selectedInbound, setSelectedInbound] = useState<InboundConfig | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);

  // Refs
  const actionSheetRef = useRef<BottomSheetModal>(null);

  // BottomSheet 配置
  const snapPoints = useMemo(() => ['50%'], []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;

  // 从serverConfig获取入站列表
  const serverConfig = getServerConfig(configId || '');
  const inbounds = serverConfig?.inbounds || [];

  // 加载入站列表
  const loadInbounds = useCallback(async () => {
    if (!currentConfig) return;

    try {
      setLoading(true);
      const result = await getThreeXUIInboundList(currentConfig);
      // 数据已经在getThreeXUIInboundList中存储到serverConfig了，这里不需要额外操作
    } catch (error) {
      console.error('Load inbounds failed:', error);
      Alert.alert(t('common.error'), t('threeXUI.inbounds.loadFailed'));
    } finally {
      setLoading(false);
    }
  }, [currentConfig]);



  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadInbounds();
    }, [loadInbounds])
  );

  const handleAddConfig = () => {
    if (!configId) return;
    router.push({
      pathname: '/3x-ui/inbound-config',
      params: { configId }
    });
  };

  const handleUserManagement = () => {
    // TODO: 实现用户管理功能
    Alert.alert(t('common.loading'), t('threeXUI.inbounds.userManagementComingSoon'));
  };

  const handleEditConfig = (inbound: InboundConfig) => {
    if (!configId) return;
    router.push({
      pathname: '/3x-ui/inbound-config',
      params: { configId, id: inbound.tag }
    });
  };

  // 格式化流量显示
  const formatTraffic = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理卡片点击
  const handleCardPress = (inbound: InboundConfig) => {
    setSelectedInbound(inbound);
    actionSheetRef.current?.present();
  };

  // 处理启用/禁用切换
  const handleToggleEnable = async (enabled: boolean) => {
    if (!selectedInbound || !currentConfig) return;

    try {
      // 构造完整的入站配置对象，只修改 enable 属性
      const updatedInbound = {
        ...selectedInbound,
        enable: enabled,
        clientStats: undefined // 删除客户端统计
      };

      const formData = new FormData();

      // 遍历配置对象，添加到表单
      Object.entries(updatedInbound).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        }
      });

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/inbound/update/${selectedInbound.id}`,
        {
          method: 'POST',
          body: formData,
        },
        currentConfig
      );

      const result = await response.json();

      if (result.success) {
        // 重新获取入站列表以更新数据
        await getThreeXUIInboundList(currentConfig);
        Alert.alert(t('common.success'), enabled ? t('threeXUI.inbounds.enableSuccess') : t('threeXUI.inbounds.disableSuccess'));
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert(t('common.error'), result.msg || t('threeXUI.inbounds.operationFailed'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inbounds.operationFailed'));
    }
  };

  // 处理导出链接
  const handleExportLinks = () => {
    actionSheetRef.current?.dismiss();
    setShowExportModal(true);
  };

  // 处理重置流量确认
  const handleResetTrafficConfirm = () => {
    Alert.alert(
      t('threeXUI.inbounds.confirmResetTraffic'),
      t('threeXUI.inbounds.confirmResetTrafficMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.inbounds.resetTraffic'),
          style: 'destructive',
          onPress: handleResetTraffic
        }
      ]
    );
  };

  // 处理重置流量
  const handleResetTraffic = async () => {
    if (!selectedInbound || !currentConfig) return;

    try {
      // 构造完整的入站配置对象，只修改流量属性
      const updatedInbound = {
        ...selectedInbound,
        up: 0,
        down: 0,
        clientStats: undefined // 删除客户端统计
      };

      const formData = new FormData();

      // 遍历配置对象，添加到表单
      Object.entries(updatedInbound).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        }
      });

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/inbound/update/${selectedInbound.id}`,
        {
          method: 'POST',
          body: formData,
        },
        currentConfig
      );

      const result = await response.json();

      if (result.success) {
        // 重新获取入站列表以更新数据
        await getThreeXUIInboundList(currentConfig);
        Alert.alert(t('common.success'), t('threeXUI.inbounds.resetSuccess'));
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert(t('common.error'), result.msg || t('threeXUI.inbounds.resetFailed'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inbounds.resetFailed'));
    }
  };

  // 处理导出JSON
  const handleExportJson = async () => {
    if (!selectedInbound) return;

    try {
      const jsonString = JSON.stringify(selectedInbound, null, 2);
      await Clipboard.setStringAsync(jsonString);
      Alert.alert(t('common.success'), t('threeXUI.inbounds.exportSuccess'));
      actionSheetRef.current?.dismiss();
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inbounds.exportFailed'));
    }
  };

  // 处理删除确认
  const handleDeleteConfirm = () => {
    Alert.alert(
      t('threeXUI.inbounds.confirmDelete'),
      t('threeXUI.inbounds.confirmDeleteMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: handleDelete
        }
      ]
    );
  };

  // 处理删除配置
  const handleDelete = async () => {
    if (!selectedInbound || !currentConfig) return;

    try {
      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/inbound/del/${selectedInbound.id}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          credentials: 'include'
        },
        currentConfig
      );

      const result = await response.json();

      if (result.success) {
        // 重新获取入站列表以更新数据
        await getThreeXUIInboundList(currentConfig);
        Alert.alert(t('common.success'), t('threeXUI.inbounds.deleteSuccess'));
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert(t('common.error'), result.msg || t('threeXUI.inbounds.deleteFailed'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.inbounds.deleteFailed'));
    }
  };

  if (!currentConfig) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: textColor }]}>
            {t('threeXUI.inbounds.configNotFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 固定在顶部的按钮组 */}
      <View style={[styles.headerContainer, { backgroundColor }]}>
        <View style={styles.header}>
          <View style={styles.buttonGroup}>
            <Button
              variant="secondary"
              size="sm"
              onPress={handleAddConfig}
              style={styles.button}
            >
              <Plus size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>{t('threeXUI.inbounds.add')}</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleUserManagement}
              style={styles.button}
            >
              <Users size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>{t('threeXUI.inbounds.userManagement')}</Text>
            </Button>
          </View>
        </View>
        <View style={[styles.headerDivider, { backgroundColor: borderColor }]} />
      </View>

      {/* 可滚动的内容区域 */}
      <ScrollView style={styles.scrollView}>
        {loading && inbounds.length === 0 ? (
          <View>
            {/* 骨架屏 */}
            {Array.from({ length: 7 }).map((_, index) => (
              <InboundSkeletonCard key={index} />
            ))}
          </View>
        ) : inbounds.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyTitle, { color: textColor }]}>
              {t('threeXUI.inbounds.noInbounds')}
            </Text>
            <Text style={[styles.emptySubtitle, { color: textColor + '80' }]}>
              {t('threeXUI.inbounds.noInboundsSubtext')}
            </Text>
          </View>
        ) : (
          <View>
            {inbounds.map((inbound: InboundConfig, index: number) => (
              <InboundCard
                key={inbound.id || index}
                inbound={inbound}
                onPress={() => handleCardPress(inbound)}
              />
            ))}
          </View>
        )}
      </ScrollView>

      {/* 操作底部弹窗 */}
      <BottomSheetModal
        ref={actionSheetRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[actionSheetStyles.content, { backgroundColor }]}>
          {selectedInbound && (
            <>
              {/* Header */}
              <View style={actionSheetStyles.header}>
                <View style={actionSheetStyles.titleContainer}>
                  <Text style={[actionSheetStyles.headerTitle, { color: textColor }]}>
                    {selectedInbound.tag || `${selectedInbound.protocol}-${selectedInbound.port}`}
                  </Text>
                  <Badge variant="default" style={actionSheetStyles.protocolBadge}>
                    <Text style={actionSheetStyles.protocolText}>{selectedInbound.protocol}</Text>
                  </Badge>
                </View>

                {/* 流量信息 */}
                {(selectedInbound.up !== undefined || selectedInbound.down !== undefined) && (
                  <View style={actionSheetStyles.trafficContainer}>
                    <View style={actionSheetStyles.trafficItem}>
                      <ArrowUp size={16} color={textColor + '80'} />
                      <Text style={[actionSheetStyles.trafficValue, { color: textColor }]}>
                        {formatTraffic(selectedInbound.up || 0)}
                      </Text>
                    </View>
                    <View style={actionSheetStyles.trafficItem}>
                      <ArrowDown size={16} color={textColor + '80'} />
                      <Text style={[actionSheetStyles.trafficValue, { color: textColor }]}>
                        {formatTraffic(selectedInbound.down || 0)}
                      </Text>
                    </View>
                  </View>
                )}
              </View>

              {/* Header分割线 */}
              <View style={[actionSheetStyles.headerDivider, { backgroundColor: borderColor }]} />

              <View style={actionSheetStyles.buttonsContainer}>
                <Button
                  variant="ghost"
                  onPress={() => {
                    actionSheetRef.current?.dismiss();
                    if (selectedInbound) handleEditConfig(selectedInbound);
                  }}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>{t('threeXUI.inbounds.editConfig')}</Text>
                </Button>

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleExportLinks}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>{t('threeXUI.inbounds.exportLinks')}</Text>
                </Button>

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleResetTrafficConfirm}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>{t('threeXUI.inbounds.resetTraffic')}</Text>
                </Button>

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleExportJson}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>{t('threeXUI.inbounds.exportJson')}</Text>
                </Button>

                {selectedInbound.enable !== undefined && (
                  <>
                    <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />
                    <Button
                      variant="ghost"
                      onPress={() => handleToggleEnable(!selectedInbound.enable)}
                      size='lg'
                    >
                      <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>
                        {selectedInbound.enable ? t('threeXUI.inbounds.disable') : t('threeXUI.inbounds.enable')}
                      </Text>
                    </Button>
                  </>
                )}

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleDeleteConfirm}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, actionSheetStyles.deleteButtonText]}>{t('threeXUI.inbounds.deleteConfig')}</Text>
                </Button>
              </View>
            </>
          )}
        </BottomSheetView>
      </BottomSheetModal>

      {/* 导出链接模态框 */}
      <ExportLinksModal
        visible={showExportModal}
        onClose={() => setShowExportModal(false)}
        inbound={selectedInbound}
        serverHost={new URL(`${currentConfig.protocol}://${currentConfig.url}`).hostname}
      />
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    // 固定在顶部的容器
  },
  header: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  headerDivider: {
    height: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
});

const actionSheetStyles = StyleSheet.create({
  content: {
    flex: 1,
  },
  header: {
    padding: 12,
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  protocolBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  protocolText: {
    fontSize: 12,
    fontWeight: '500',
  },
  trafficContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  trafficItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  trafficValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerDivider: {
    height: 1,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  actionButton: {
    paddingVertical: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
});
